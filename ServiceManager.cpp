#include "ServiceManager.h"
#include <QDebug>
#include <QMessageBox>
#include <QFileInfo>
#include <QSettings>

#ifdef Q_OS_WIN
#include <shellapi.h>
#include <lmcons.h>
#endif

// 静态常量定义
const QString ServiceManager::DEFAULT_SERVICE_NAME = "MonitorWhiteCtrlService";
const QString ServiceManager::DEFAULT_DISPLAY_NAME = "显示器管控服务";
const QString ServiceManager::DEFAULT_DESCRIPTION = "显示器白名单管控服务，监控显示器EDID信息和USB设备";
const QString ServiceManager::REGISTRY_RUN_KEY = "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run";

ServiceManager::ServiceManager(QObject *parent)
    : QObject(parent)
    , m_serviceName(DEFAULT_SERVICE_NAME)
    , m_serviceDisplayName(DEFAULT_DISPLAY_NAME)
    , m_serviceDescription(DEFAULT_DESCRIPTION)
{
    m_executablePath = getCurrentExecutablePath();
    qDebug() << "ServiceManager initialized";
    qDebug() << "Executable path:" << m_executablePath;
}

ServiceManager::~ServiceManager()
{
}

QString ServiceManager::getCurrentExecutablePath()
{
    return QCoreApplication::applicationFilePath();
}

bool ServiceManager::isRunningAsAdmin()
{
#ifdef Q_OS_WIN
    BOOL isAdmin = FALSE;
    PSID adminGroup = nullptr;
    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;
    
    if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
        CheckTokenMembership(nullptr, adminGroup, &isAdmin);
        FreeSid(adminGroup);
    }
    
    return isAdmin == TRUE;
#else
    return false;
#endif
}

bool ServiceManager::requestAdminPrivileges()
{
#ifdef Q_OS_WIN
    if (isRunningAsAdmin()) {
        return true;
    }
    
    QString program = QCoreApplication::applicationFilePath();
    QString arguments = "--admin-mode";
    
    // 使用ShellExecute请求管理员权限
    HINSTANCE result = ShellExecute(nullptr, L"runas", 
                                   reinterpret_cast<LPCWSTR>(program.utf16()),
                                   reinterpret_cast<LPCWSTR>(arguments.utf16()),
                                   nullptr, SW_SHOWNORMAL);
    
    return reinterpret_cast<int>(result) > 32;
#else
    return false;
#endif
}

bool ServiceManager::setAutoStart(bool enable)
{
    if (enable) {
        qDebug() << "Enabling auto-start on boot";
    } else {
        qDebug() << "Disabling auto-start on boot";
    }
    
    return setRegistryAutoStart(enable);
}

bool ServiceManager::isAutoStartEnabled()
{
    return getRegistryAutoStart();
}

bool ServiceManager::setRegistryAutoStart(bool enable)
{
    QSettings settings(REGISTRY_RUN_KEY, QSettings::NativeFormat);
    
    if (enable) {
        // 添加到启动项
        QString startupCommand = QString("\"%1\" --startup").arg(m_executablePath);
        settings.setValue(m_serviceName, startupCommand);
        qDebug() << "Added to registry startup:" << startupCommand;
    } else {
        // 从启动项移除
        settings.remove(m_serviceName);
        qDebug() << "Removed from registry startup";
    }
    
    settings.sync();
    return settings.status() == QSettings::NoError;
}

bool ServiceManager::getRegistryAutoStart()
{
    QSettings settings(REGISTRY_RUN_KEY, QSettings::NativeFormat);
    return settings.contains(m_serviceName);
}

void ServiceManager::setServiceName(const QString &serviceName)
{
    m_serviceName = serviceName;
}

void ServiceManager::setServiceDisplayName(const QString &displayName)
{
    m_serviceDisplayName = displayName;
}

void ServiceManager::setServiceDescription(const QString &description)
{
    m_serviceDescription = description;
}

QString ServiceManager::getServiceName() const
{
    return m_serviceName;
}

QString ServiceManager::getServiceDisplayName() const
{
    return m_serviceDisplayName;
}

QString ServiceManager::getServiceDescription() const
{
    return m_serviceDescription;
}

#ifdef Q_OS_WIN
bool ServiceManager::installService()
{
    if (!isRunningAsAdmin()) {
        qWarning() << "Installing service requires administrator privileges";
        return false;
    }
    
    SC_HANDLE hSCManager = openServiceManager();
    if (!hSCManager) {
        qWarning() << "Unable to open service control manager";
        return false;
    }
    
    // 检查服务是否已存在
    SC_HANDLE hService = openService(hSCManager);
    if (hService) {
        qDebug() << "Service already exists:" << m_serviceName;
        closeServiceHandle(hService);
        closeServiceHandle(hSCManager);
        return true;
    }
    
    // 创建服务
    QString serviceCommand = QString("\"%1\" --service").arg(m_executablePath);
    
    hService = CreateService(
        hSCManager,
        reinterpret_cast<LPCWSTR>(m_serviceName.utf16()),
        reinterpret_cast<LPCWSTR>(m_serviceDisplayName.utf16()),
        SERVICE_ALL_ACCESS,
        SERVICE_WIN32_OWN_PROCESS,
        SERVICE_AUTO_START,
        SERVICE_ERROR_NORMAL,
        reinterpret_cast<LPCWSTR>(serviceCommand.utf16()),
        nullptr, nullptr, nullptr, nullptr, nullptr
    );
    
    if (!hService) {
        DWORD error = GetLastError();
        qWarning() << "Failed to create service, error code:" << error;
        closeServiceHandle(hSCManager);
        return false;
    }
    
    // 设置服务描述
    SERVICE_DESCRIPTION sd;
    sd.lpDescription = const_cast<LPWSTR>(reinterpret_cast<LPCWSTR>(m_serviceDescription.utf16()));
    ChangeServiceConfig2(hService, SERVICE_CONFIG_DESCRIPTION, &sd);
    
    qDebug() << "Service installed successfully:" << m_serviceName;
    
    closeServiceHandle(hService);
    closeServiceHandle(hSCManager);
    
    emit serviceInstalled();
    return true;
}

bool ServiceManager::uninstallService()
{
    if (!isRunningAsAdmin()) {
        qWarning() << "卸载服务需要管理员权限";
        return false;
    }
    
    SC_HANDLE hSCManager = openServiceManager();
    if (!hSCManager) {
        return false;
    }
    
    SC_HANDLE hService = openService(hSCManager);
    if (!hService) {
        qDebug() << "服务不存在:" << m_serviceName;
        closeServiceHandle(hSCManager);
        return true;
    }
    
    // 停止服务
    SERVICE_STATUS status;
    ControlService(hService, SERVICE_CONTROL_STOP, &status);
    
    // 删除服务
    BOOL result = DeleteService(hService);
    
    closeServiceHandle(hService);
    closeServiceHandle(hSCManager);
    
    if (result) {
        qDebug() << "服务卸载成功:" << m_serviceName;
        emit serviceUninstalled();
        return true;
    } else {
        qWarning() << "服务卸载失败";
        return false;
    }
}

bool ServiceManager::startService()
{
    SC_HANDLE hSCManager = openServiceManager();
    if (!hSCManager) {
        return false;
    }
    
    SC_HANDLE hService = openService(hSCManager);
    if (!hService) {
        closeServiceHandle(hSCManager);
        return false;
    }
    
    BOOL result = StartService(hService, 0, nullptr);
    
    closeServiceHandle(hService);
    closeServiceHandle(hSCManager);
    
    if (result) {
        qDebug() << "服务启动成功:" << m_serviceName;
        emit serviceStarted();
        return true;
    } else {
        DWORD error = GetLastError();
        if (error == ERROR_SERVICE_ALREADY_RUNNING) {
            qDebug() << "服务已在运行:" << m_serviceName;
            return true;
        }
        qWarning() << "服务启动失败，错误代码:" << error;
        return false;
    }
}

bool ServiceManager::stopService()
{
    SC_HANDLE hSCManager = openServiceManager();
    if (!hSCManager) {
        return false;
    }
    
    SC_HANDLE hService = openService(hSCManager);
    if (!hService) {
        closeServiceHandle(hSCManager);
        return false;
    }
    
    SERVICE_STATUS status;
    BOOL result = ControlService(hService, SERVICE_CONTROL_STOP, &status);
    
    closeServiceHandle(hService);
    closeServiceHandle(hSCManager);
    
    if (result) {
        qDebug() << "服务停止成功:" << m_serviceName;
        emit serviceStopped();
        return true;
    } else {
        qWarning() << "服务停止失败";
        return false;
    }
}

bool ServiceManager::restartService()
{
    return stopService() && startService();
}

ServiceManager::ServiceStatus ServiceManager::getServiceStatus()
{
    SC_HANDLE hSCManager = openServiceManager();
    if (!hSCManager) {
        return ServiceUnknown;
    }
    
    SC_HANDLE hService = openService(hSCManager);
    if (!hService) {
        closeServiceHandle(hSCManager);
        return ServiceNotInstalled;
    }
    
    SERVICE_STATUS status;
    if (!QueryServiceStatus(hService, &status)) {
        closeServiceHandle(hService);
        closeServiceHandle(hSCManager);
        return ServiceUnknown;
    }
    
    closeServiceHandle(hService);
    closeServiceHandle(hSCManager);
    
    return convertWindowsStatus(status.dwCurrentState);
}

QString ServiceManager::getServiceStatusString()
{
    ServiceStatus status = getServiceStatus();
    
    switch (status) {
    case ServiceNotInstalled:   return "未安装";
    case ServiceStopped:        return "已停止";
    case ServiceStartPending:   return "启动中";
    case ServiceStopPending:    return "停止中";
    case ServiceRunning:        return "运行中";
    case ServiceContinuePending: return "继续中";
    case ServicePausePending:   return "暂停中";
    case ServicePaused:         return "已暂停";
    case ServiceUnknown:        return "未知";
    default:                    return "未知";
    }
}

bool ServiceManager::isServiceInstalled()
{
    return getServiceStatus() != ServiceNotInstalled;
}

bool ServiceManager::isServiceRunning()
{
    return getServiceStatus() == ServiceRunning;
}

SC_HANDLE ServiceManager::openServiceManager()
{
    return OpenSCManager(nullptr, nullptr, SC_MANAGER_ALL_ACCESS);
}

SC_HANDLE ServiceManager::openService(SC_HANDLE hSCManager)
{
    return OpenService(hSCManager, reinterpret_cast<LPCWSTR>(m_serviceName.utf16()), SERVICE_ALL_ACCESS);
}

void ServiceManager::closeServiceHandle(SC_HANDLE handle)
{
    if (handle) {
        CloseServiceHandle(handle);
    }
}

ServiceManager::ServiceStatus ServiceManager::convertWindowsStatus(DWORD dwCurrentState)
{
    switch (dwCurrentState) {
    case SERVICE_STOPPED:           return ServiceStopped;
    case SERVICE_START_PENDING:     return ServiceStartPending;
    case SERVICE_STOP_PENDING:      return ServiceStopPending;
    case SERVICE_RUNNING:           return ServiceRunning;
    case SERVICE_CONTINUE_PENDING:  return ServiceContinuePending;
    case SERVICE_PAUSE_PENDING:     return ServicePausePending;
    case SERVICE_PAUSED:            return ServicePaused;
    default:                        return ServiceUnknown;
    }
}

#else
// 非Windows平台的空实现
bool ServiceManager::installService() { return false; }
bool ServiceManager::uninstallService() { return false; }
bool ServiceManager::startService() { return false; }
bool ServiceManager::stopService() { return false; }
bool ServiceManager::restartService() { return false; }
ServiceManager::ServiceStatus ServiceManager::getServiceStatus() { return ServiceNotInstalled; }
QString ServiceManager::getServiceStatusString() { return "不支持"; }
bool ServiceManager::isServiceInstalled() { return false; }
bool ServiceManager::isServiceRunning() { return false; }
#endif
