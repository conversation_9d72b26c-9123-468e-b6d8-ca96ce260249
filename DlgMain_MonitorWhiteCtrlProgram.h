#ifndef DLGMAIN_MONITORWHITECTRLPROGRAM_H
#define DLGMAIN_MONITORWHITECTRLPROGRAM_H

#include <QMainWindow>
#include <QDebug>
#include <QFile>
#include <QMutex>
#include <QProcessEnvironment>
#include <QDateTime>
#include <QTextStream>
#include <QCloseEvent>
#include <QEvent>

#ifdef Q_OS_WIN
#include <windows.h>
#else
#include <unistd.h>
#endif

#include "EDIDManager.h"

// 前置声明
class TrayManager;
class WhiteListManager;
class USBDeviceMonitor;
class ServiceManager;

namespace Ui {
class CDlgMain_MonitorWhiteCtrlProgram;
}

class CDlgMain_MonitorWhiteCtrlProgram : public QMainWindow
{
    Q_OBJECT

public:
    explicit CDlgMain_MonitorWhiteCtrlProgram(QWidget *parent = 0);
    virtual ~CDlgMain_MonitorWhiteCtrlProgram();

    // 获取管理器实例
    TrayManager* getTrayManager() const { return m_trayManager; }
    WhiteListManager* getWhiteListManager() const { return m_whiteListManager; }
    USBDeviceMonitor* getUSBDeviceMonitor() const { return m_usbDeviceMonitor; }
    ServiceManager* getServiceManager() const { return m_serviceManager; }

protected:
    // 重写窗口事件
    void closeEvent(QCloseEvent *event) override;
    void changeEvent(QEvent *event) override;

public slots:
    // 托盘相关槽函数
    void onClearWhiteList();
    void onSyncWhiteList();
    void showMainWindow();
    void hideToTray();

private slots:
    void onDisplaysChanged();    // 显示器配置改变槽函数
    void onWhiteListUpdated(int count);
    void onUSBKeyDetected(const QString &usbPath);
    void onSyncCompleted(bool success, const QString &message);

    void on_pushButton_clicked();

private:
    // 初始化函数
    void initEDIDManager();
    void initTrayManager();
    void initWhiteListManager();
    void initUSBDeviceMonitor();
    void initServiceManager();
    void autoStartServiceOnStartup();

    // 显示器管理功能
    void refreshDisplaysInfo();
    void showDisplaysInfo();
    void updateWindowTitle();

    // 日志功能
    void WriteLog(const QString &message);
    QString getSystemUserName();

private:
    Ui::CDlgMain_MonitorWhiteCtrlProgram    *ui;

    // 核心管理器
    EDIDManager                             *m_edidManager;
    TrayManager                             *m_trayManager;
    WhiteListManager                        *m_whiteListManager;
    USBDeviceMonitor                        *m_usbDeviceMonitor;
    ServiceManager                          *m_serviceManager;

    // 日志相关
    QMutex                                  m_mutex_Log;
    QFile                                   m_file_Log;
    QTextStream                             m_textStream_Out;

    // 状态标志
    bool                                    m_isInitialized;
};

#endif // DLGMAIN_MONITORWHITECTRLPROGRAM_H
