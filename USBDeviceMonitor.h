#ifndef USBDEVICEMONITOR_H
#define USBDEVICEMONITOR_H

#include <QObject>
#include <QTimer>
#include <QStringList>
#include <QStorageInfo>
#include <QFileSystemWatcher>

#ifdef Q_OS_WIN
#include <windows.h>
#include <dbt.h>
#include <QAbstractNativeEventFilter>
#endif

// 前置声明
class WhiteListManager;

/**
 * @brief USB设备监控器
 * 
 * 监控USB设备的插入和移除
 * 自动检测包含白名单的USB Key
 */
class USBDeviceMonitor : public QObject
#ifdef Q_OS_WIN
    , public QAbstractNativeEventFilter
#endif
{
    Q_OBJECT

public:
    explicit USBDeviceMonitor(WhiteListManager *whiteListManager, QObject *parent = nullptr);
    ~USBDeviceMonitor();

    // 启动/停止监控
    void startMonitoring();
    void stopMonitoring();
    
    // 获取当前连接的USB设备
    QStringList getCurrentUSBDevices();
    
    // 手动扫描USB设备
    void scanUSBDevices();

#ifdef Q_OS_WIN
    // Windows原生事件过滤器
    bool nativeEventFilter(const QByteArray &eventType, void *message, long *result) override;
#endif

signals:
    // USB设备插入信号
    void usbDeviceInserted(const QString &devicePath);
    
    // USB设备移除信号
    void usbDeviceRemoved(const QString &devicePath);
    
    // 发现包含白名单的USB Key
    void whiteListUSBKeyDetected(const QString &usbPath);

public slots:
    // 处理USB设备变化
    void onUSBDeviceChanged();
    
    // 处理白名单USB Key检测
    void onWhiteListUSBKeyDetected(const QString &usbPath);

private slots:
    // 定时扫描USB设备
    void onScanTimer();

private:
    void initializeMonitoring();
    void cleanupMonitoring();
    
    // 检查USB设备是否包含白名单
    bool checkForWhiteList(const QString &usbPath);
    
    // 比较USB设备列表
    void compareUSBDevices(const QStringList &newDevices);

private:
    WhiteListManager *m_whiteListManager;
    QTimer *m_scanTimer;
    QStringList m_lastUSBDevices;
    bool m_isMonitoring;
    
#ifdef Q_OS_WIN
    // Windows特定的设备通知
    HDEVNOTIFY m_deviceNotify;
    void registerDeviceNotification();
    void unregisterDeviceNotification();
#endif

    // 配置参数
    static const int SCAN_INTERVAL_MS;
    static const QString WHITE_LIST_FILE_NAME;
};

#endif // USBDEVICEMONITOR_H
