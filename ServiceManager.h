#ifndef SERVICEMANAGER_H
#define SERVICEMANAGER_H

#include <QObject>
#include <QString>
#include <QProcess>
#include <QSettings>
#include <QCoreApplication>
#include <QDir>

#ifdef Q_OS_WIN
#include <windows.h>
#include <winsvc.h>
#endif

/**
 * @brief Windows服务管理器
 * 
 * 管理程序的Windows服务安装、启动、停止等功能
 * 支持开机自启动配置
 */
class ServiceManager : public QObject
{
    Q_OBJECT

public:
    // 服务状态枚举
    enum ServiceStatus {
        ServiceNotInstalled = 0,
        ServiceStopped = 1,
        ServiceStartPending = 2,
        ServiceStopPending = 3,
        ServiceRunning = 4,
        ServiceContinuePending = 5,
        ServicePausePending = 6,
        ServicePaused = 7,
        ServiceUnknown = 8
    };

public:
    explicit ServiceManager(QObject *parent = nullptr);
    ~ServiceManager();

    // 服务管理
    bool installService();
    bool uninstallService();
    bool startService();
    bool stopService();
    bool restartService();
    
    // 服务状态查询
    ServiceStatus getServiceStatus();
    QString getServiceStatusString();
    bool isServiceInstalled();
    bool isServiceRunning();
    
    // 开机启动管理
    bool setAutoStart(bool enable);
    bool isAutoStartEnabled();
    
    // 服务配置
    void setServiceName(const QString &serviceName);
    void setServiceDisplayName(const QString &displayName);
    void setServiceDescription(const QString &description);
    
    QString getServiceName() const;
    QString getServiceDisplayName() const;
    QString getServiceDescription() const;
    
    // 获取当前程序路径
    QString getCurrentExecutablePath();
    
    // 检查管理员权限
    bool isRunningAsAdmin();
    bool requestAdminPrivileges();

signals:
    void serviceStatusChanged(ServiceStatus status);
    void serviceInstalled();
    void serviceUninstalled();
    void serviceStarted();
    void serviceStopped();

private:
#ifdef Q_OS_WIN
    // Windows服务相关
    SC_HANDLE openServiceManager();
    SC_HANDLE openService(SC_HANDLE hSCManager);
    void closeServiceHandle(SC_HANDLE handle);
    
    ServiceStatus convertWindowsStatus(DWORD dwCurrentState);
#endif
    
    // 注册表操作
    bool setRegistryAutoStart(bool enable);
    bool getRegistryAutoStart();

private:
    QString m_serviceName;
    QString m_serviceDisplayName;
    QString m_serviceDescription;
    QString m_executablePath;
    
    // 默认配置
    static const QString DEFAULT_SERVICE_NAME;
    static const QString DEFAULT_DISPLAY_NAME;
    static const QString DEFAULT_DESCRIPTION;
    static const QString REGISTRY_RUN_KEY;
};

#endif // SERVICEMANAGER_H
