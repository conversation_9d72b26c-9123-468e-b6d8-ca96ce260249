@echo off
echo ========================================
echo 测试服务卸载功能
echo ========================================
echo.

echo 1. 检查当前服务状态...
sc query MonitorWhiteCtrlService
echo.

echo 2. 检查注册表自启动项...
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v MonitorWhiteCtrlService
echo.

echo 3. 运行程序测试卸载功能...
echo 请在程序中点击"卸载服务"按钮进行测试
echo.

echo 4. 等待用户操作...
pause

echo.
echo 5. 验证卸载结果...
echo.

echo 检查服务是否已删除:
sc query MonitorWhiteCtrlService
echo.

echo 检查注册表项是否已删除:
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v MonitorWhiteCtrlService
echo.

echo 检查系统注册表项是否已删除:
reg query "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v MonitorWhiteCtrlService
echo.

echo ========================================
echo 测试完成
echo ========================================
pause
