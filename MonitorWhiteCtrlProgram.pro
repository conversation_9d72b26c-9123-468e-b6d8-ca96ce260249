#-------------------------------------------------
#
# Project created by QtCreator 2025-05-28T16:16:49
#
#-------------------------------------------------

QT       += core gui

win32 {
    LIBS += -lsetupapi -ladvapi32 -luser32
    DEFINES += UNICODE _UNICODE
}

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = 001_MonitorWhiteCtrlProgram
TEMPLATE = app


SOURCES += main.cpp\
        DlgMain_MonitorWhiteCtrlProgram.cpp\
        EDIDManager.cpp\
        TrayManager.cpp\
        WhiteListManager.cpp\
        USBDeviceMonitor.cpp\
        ServiceManager.cpp

HEADERS  += DlgMain_MonitorWhiteCtrlProgram.h\
        EDIDManager.h\
        TrayManager.h\
        WhiteListManager.h\
        USBDeviceMonitor.h\
        ServiceManager.h

FORMS    += DlgMain_MonitorWhiteCtrlProgram.ui
