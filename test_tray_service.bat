@echo off
echo ========================================
echo 测试托盘服务管理功能
echo ========================================
echo.

echo 1. 检查当前服务状态...
sc query MonitorWhiteCtrlService
echo.

echo 2. 检查注册表自启动项...
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v MonitorWhiteCtrlService 2>nul
if %errorlevel% equ 0 (
    echo 注册表自启动项存在
) else (
    echo 注册表自启动项不存在
)
echo.

echo 3. 启动程序进行托盘测试...
echo 请按照以下步骤测试：
echo.
echo 步骤1: 程序启动后，查看系统托盘图标
echo 步骤2: 右键点击托盘图标，查看菜单项
echo 步骤3: 测试"开启服务"功能
echo 步骤4: 测试"卸载服务"功能
echo 步骤5: 观察菜单项状态变化
echo.

echo 4. 启动程序...
start "" "E:\Work\Project\001_MonitorWhiteCtrlProgram\001_Code\build-001_MonitorWhiteCtrlProgram-Desktop_Qt_5_6_3_MinGW_32bit-Debug\debug\001_MonitorWhiteCtrlProgram.exe"

echo.
echo 5. 等待测试完成...
echo 请在程序中测试托盘功能，完成后按任意键继续验证结果
pause

echo.
echo 6. 验证测试结果...
echo.

echo 检查服务状态:
sc query MonitorWhiteCtrlService
echo.

echo 检查注册表自启动项:
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v MonitorWhiteCtrlService 2>nul
if %errorlevel% equ 0 (
    echo 注册表自启动项存在
    reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v MonitorWhiteCtrlService
) else (
    echo 注册表自启动项不存在
)
echo.

echo ========================================
echo 测试完成
echo ========================================
echo.
echo 预期结果：
echo 1. 托盘图标正常显示
echo 2. 右键菜单包含"开启服务"和"卸载服务"选项
echo 3. 菜单项状态根据服务状态动态更新
echo 4. 服务操作功能正常工作
echo 5. 操作完成后有相应的通知消息
echo.
pause
